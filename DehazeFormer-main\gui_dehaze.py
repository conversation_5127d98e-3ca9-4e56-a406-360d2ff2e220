#!/usr/bin/env python3
"""
DehazeFormer GUI Application
A simple graphical interface for image dehazing using DehazeFormer model
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import torch
import numpy as np
from collections import OrderedDict
import threading

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils import read_img, write_img, hwc_to_chw, chw_to_hwc
from models import *


class DehazeFormerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("DehazeFormer - 图像去雾工具")
        self.root.geometry("800x600")
        
        # Variables
        self.input_image_path = None
        self.output_image_path = None
        self.model = None
        self.original_image = None
        self.dehazed_image = None
        
        # Model path
        self.model_path = "D:\PAchongtext\dan107\DehazeFormer-main\configs\indoor\dehazeformer-l.pth"
        
        self.setup_ui()
        self.load_model()
    
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="DehazeFormer 图像去雾", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # File selection frame
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # Input file selection
        ttk.Label(file_frame, text="输入图片:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.input_path_var = tk.StringVar()
        input_entry = ttk.Entry(file_frame, textvariable=self.input_path_var, state="readonly")
        input_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(file_frame, text="选择图片", command=self.select_input_image).grid(row=0, column=2)
        
        # Output file selection
        ttk.Label(file_frame, text="输出路径:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.output_path_var = tk.StringVar()
        output_entry = ttk.Entry(file_frame, textvariable=self.output_path_var, state="readonly")
        output_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))
        ttk.Button(file_frame, text="选择保存位置", command=self.select_output_path).grid(row=1, column=2, pady=(10, 0))
        
        # Image display frame
        image_frame = ttk.LabelFrame(main_frame, text="图像预览", padding="10")
        image_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        image_frame.columnconfigure(0, weight=1)
        image_frame.columnconfigure(1, weight=1)
        image_frame.rowconfigure(1, weight=1)
        
        # Original image
        ttk.Label(image_frame, text="原始图片", font=("Arial", 12, "bold")).grid(row=0, column=0, pady=(0, 10))
        self.original_canvas = tk.Canvas(image_frame, width=350, height=250, bg="white", relief="sunken", bd=2)
        self.original_canvas.grid(row=1, column=0, padx=(0, 5), sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Dehazed image
        ttk.Label(image_frame, text="去雾后图片", font=("Arial", 12, "bold")).grid(row=0, column=1, pady=(0, 10))
        self.dehazed_canvas = tk.Canvas(image_frame, width=350, height=250, bg="white", relief="sunken", bd=2)
        self.dehazed_canvas.grid(row=1, column=1, padx=(5, 0), sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Control frame
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10))
        
        # Process button
        self.process_btn = ttk.Button(control_frame, text="开始去雾", command=self.process_image, 
                                     style="Accent.TButton")
        self.process_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Save button
        self.save_btn = ttk.Button(control_frame, text="保存结果", command=self.save_result, state="disabled")
        self.save_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Progress bar
        self.progress = ttk.Progressbar(control_frame, mode='indeterminate')
        self.progress.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)
        
        # Status frame
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(0, weight=1)
        
        # Status label
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.grid(row=0, column=0, sticky=tk.W)
        
        # Model status
        self.model_status_var = tk.StringVar()
        model_status_label = ttk.Label(status_frame, textvariable=self.model_status_var)
        model_status_label.grid(row=0, column=1, sticky=tk.E)
    
    def load_model(self):
        """Load the DehazeFormer model"""
        def load_in_thread():
            try:
                self.status_var.set("正在加载模型...")
                self.model_status_var.set("加载中...")
                
                if not os.path.exists(self.model_path):
                    self.status_var.set(f"错误: 模型文件未找到 {self.model_path}")
                    self.model_status_var.set("模型未加载")
                    return
                
                # Load model
                self.model = dehazeformer_l()
                
                # Load weights
                state_dict = torch.load(self.model_path, map_location='cpu')['state_dict']
                new_state_dict = OrderedDict()
                
                for k, v in state_dict.items():
                    if k.startswith('module.'):
                        name = k[7:]
                    else:
                        name = k
                    new_state_dict[name] = v
                
                self.model.load_state_dict(new_state_dict)
                self.model.eval()
                
                # Move to GPU if available
                if torch.cuda.is_available():
                    self.model.cuda()
                    device_info = "GPU"
                else:
                    device_info = "CPU"
                
                self.status_var.set("模型加载完成")
                self.model_status_var.set(f"DehazeFormer-L ({device_info})")
                
            except Exception as e:
                self.status_var.set(f"模型加载失败: {str(e)}")
                self.model_status_var.set("模型未加载")
        
        # Load model in background thread
        threading.Thread(target=load_in_thread, daemon=True).start()
    
    def select_input_image(self):
        """Select input image file"""
        file_path = filedialog.askopenfilename(
            title="选择要去雾的图片",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
                ("JPEG", "*.jpg *.jpeg"),
                ("PNG", "*.png"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.input_image_path = file_path
            self.input_path_var.set(file_path)
            self.load_and_display_image(file_path, self.original_canvas)
            
            # Auto-generate output path
            if not self.output_path_var.get():
                name, ext = os.path.splitext(file_path)
                auto_output = f"{name}_dehazed{ext}"
                self.output_path_var.set(auto_output)
    
    def select_output_path(self):
        """Select output file path"""
        file_path = filedialog.asksaveasfilename(
            title="选择保存位置",
            defaultextension=".jpg",
            filetypes=[
                ("JPEG", "*.jpg"),
                ("PNG", "*.png"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.output_image_path = file_path
            self.output_path_var.set(file_path)
    
    def load_and_display_image(self, image_path, canvas):
        """Load and display image on canvas"""
        try:
            # Load image
            pil_image = Image.open(image_path)
            
            # Resize to fit canvas
            canvas_width = canvas.winfo_width() or 350
            canvas_height = canvas.winfo_height() or 250
            
            # Calculate resize ratio
            img_width, img_height = pil_image.size
            ratio = min(canvas_width / img_width, canvas_height / img_height)
            new_width = int(img_width * ratio)
            new_height = int(img_height * ratio)
            
            # Resize image
            pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(pil_image)
            
            # Display on canvas
            canvas.delete("all")
            canvas.create_image(canvas_width // 2, canvas_height // 2, image=photo)
            canvas.image = photo  # Keep a reference
            
        except Exception as e:
            messagebox.showerror("错误", f"无法加载图片: {str(e)}")
    
    def process_image(self):
        """Process the image with DehazeFormer"""
        if not self.input_image_path:
            messagebox.showwarning("警告", "请先选择输入图片")
            return
        
        if not self.model:
            messagebox.showerror("错误", "模型未加载")
            return
        
        def process_in_thread():
            try:
                self.progress.start()
                self.process_btn.config(state="disabled")
                self.status_var.set("正在处理图片...")
                
                # Read and preprocess image
                img = read_img(self.input_image_path)
                img = img * 2 - 1  # Scale to [-1, 1]
                
                # Convert to tensor
                img_tensor = torch.from_numpy(hwc_to_chw(img)).unsqueeze(0)
                
                if torch.cuda.is_available():
                    img_tensor = img_tensor.cuda()
                
                # Inference
                with torch.no_grad():
                    output = self.model(img_tensor).clamp_(-1, 1)
                    output = output * 0.5 + 0.5  # Convert back to [0, 1]
                    self.dehazed_image = chw_to_hwc(output.detach().cpu().squeeze(0).numpy())
                
                # Display result
                self.root.after(0, self.display_result)
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"处理失败: {str(e)}"))
            finally:
                self.root.after(0, self.processing_finished)
        
        # Process in background thread
        threading.Thread(target=process_in_thread, daemon=True).start()
    
    def display_result(self):
        """Display the dehazed result"""
        if self.dehazed_image is not None:
            # Convert numpy array to PIL Image
            img_uint8 = (self.dehazed_image * 255).astype(np.uint8)
            pil_image = Image.fromarray(img_uint8)
            
            # Display on canvas
            canvas_width = self.dehazed_canvas.winfo_width() or 350
            canvas_height = self.dehazed_canvas.winfo_height() or 250
            
            # Resize to fit canvas
            img_width, img_height = pil_image.size
            ratio = min(canvas_width / img_width, canvas_height / img_height)
            new_width = int(img_width * ratio)
            new_height = int(img_height * ratio)
            
            pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(pil_image)
            
            self.dehazed_canvas.delete("all")
            self.dehazed_canvas.create_image(canvas_width // 2, canvas_height // 2, image=photo)
            self.dehazed_canvas.image = photo
            
            self.save_btn.config(state="normal")
            self.status_var.set("处理完成")
    
    def processing_finished(self):
        """Called when processing is finished"""
        self.progress.stop()
        self.process_btn.config(state="normal")
    
    def save_result(self):
        """Save the dehazed result"""
        if self.dehazed_image is None:
            messagebox.showwarning("警告", "没有可保存的结果")
            return
        
        output_path = self.output_path_var.get()
        if not output_path:
            messagebox.showwarning("警告", "请选择保存位置")
            return
        
        try:
            write_img(output_path, self.dehazed_image)
            messagebox.showinfo("成功", f"图片已保存到: {output_path}")
            self.status_var.set(f"已保存到: {os.path.basename(output_path)}")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")


def main():
    root = tk.Tk()
    app = DehazeFormerGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
