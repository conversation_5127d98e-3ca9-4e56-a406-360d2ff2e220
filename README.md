# 车牌图像去雾处理 - FFA-Net

## 项目概述

本项目使用FFA-Net（Feature Fusion Attention Network）模型对雾霾车牌图像进行去雾处理。FFA-Net是一种先进的单图像去雾网络，通过特征融合注意力机制有效地去除图像中的雾霾，恢复清晰的图像细节。

## 数据集

本项目使用的数据集是Foggy-Hazy License Plates数据集，包含雾霾车牌图像和对应的清晰车牌图像。数据集已经划分为训练集和测试集：

- 训练集：800对图像（清晰/雾霾）
- 测试集：201对图像（清晰/雾霾）

## 文件结构

```
.
├── FFA-Net-master/            # FFA-Net模型源代码
├── dataset_split/             # 数据集
│   ├── train/                 # 训练集
│   │   ├── clear/            # 清晰图像
│   │   └── hazy/             # 雾霾图像
│   ├── test/                  # 测试集
│   │   ├── clear/            # 清晰图像
│   │   └── hazy/             # 雾霾图像
│   ├── results/               # 去雾结果（运行后生成）
│   └── visualization/         # 可视化比较（运行后生成）
├── dehaze_license_plates.py   # 去雾处理脚本
├── evaluate_results.py        # 结果评估脚本
├── run_dehaze.bat             # 一键运行批处理脚本
└── README.md                  # 项目说明文档
```

## 使用方法

### 方法一：使用批处理脚本（推荐）

直接双击运行 `run_dehaze.bat` 批处理文件，它会自动执行以下步骤：
1. 创建必要的输出目录
2. 运行去雾处理
3. 评估去雾结果

### 方法二：手动运行

1. 运行去雾处理：
   ```
   python dehaze_license_plates.py
   ```

2. 评估去雾结果：
   ```
   python evaluate_results.py
   ```

## 结果说明

- 去雾结果保存在 `dataset_split/results/` 目录下
- 可视化比较保存在 `dataset_split/visualization/` 目录下
- 评估指标（PSNR和SSIM）保存在 `dataset_split/results/evaluation_results.txt` 文件中

## 模型说明

FFA-Net是一种基于特征融合注意力机制的去雾网络，具有以下特点：

1. 使用特征融合注意力机制有效提取和融合图像特征
2. 采用通道注意力和像素注意力双重机制
3. 多组特征处理和融合，提高去雾效果

本项目使用的FFA-Net模型配置：
- 组数（gps）：3
- 块数（blocks）：19

## 注意事项

1. 确保已安装所需的Python库：
   ```
   pip install torch torchvision numpy opencv-python pillow matplotlib tqdm
   ```

2. 确保预训练模型文件（`its_train_ffa_3_19.pk`）位于项目根目录

3. 如果使用GPU加速，请确保已正确安装CUDA和cuDNN