# Foggy-Hazy License Plates 数据集划分

## 数据集概述
这是一个用于车牌图像去雾的配对数据集，包含雾霾车牌图像和对应的清晰车牌图像。

## 原始数据集结构
```
Foggy-Hazy License Plates Images/
├── Ground Truth images/
│   └── Ground Truth images/     # 清晰车牌图像 (999张)
└── New Hazy dataset/
    └── New Hazy dataset/        # 雾霾车牌图像 (999张)
```

## 划分后的数据集结构
```
dataset_split/
├── train/                      # 训练集 (800对图像)
│   ├── clear/                  # 清晰车牌图像 (800张)
│   └── hazy/                   # 雾霾车牌图像 (800张)
└── test/                       # 测试集 (201对图像)
    ├── clear/                  # 清晰车牌图像 (201张)
    └── hazy/                   # 雾霾车牌图像 (201张)
```

## 数据集统计信息
- **总图像对数**: 1001对
- **训练集**: 800对 (80%)
- **测试集**: 201对 (20%)
- **图像格式**: 
  - 清晰图像: JPG/PNG
  - 雾霾图像: PNG
- **随机种子**: 42 (确保可重复性)

## 使用说明

### 1. 数据加载
```python
import os
from PIL import Image

def load_image_pair(clear_path, hazy_path):
    clear_img = Image.open(clear_path)
    hazy_img = Image.open(hazy_path)
    return clear_img, hazy_img

# 训练集
train_clear_dir = "dataset_split/train/clear"
train_hazy_dir = "dataset_split/train/hazy"

# 测试集
test_clear_dir = "dataset_split/test/clear"
test_hazy_dir = "dataset_split/test/hazy"
```

### 2. PyTorch数据集类示例
```python
import torch
from torch.utils.data import Dataset
from torchvision import transforms
from PIL import Image
import os

class DehazingDataset(Dataset):
    def __init__(self, clear_dir, hazy_dir, transform=None):
        self.clear_dir = clear_dir
        self.hazy_dir = hazy_dir
        self.transform = transform
        
        # 获取所有文件名
        self.clear_files = sorted(os.listdir(clear_dir))
        self.hazy_files = sorted(os.listdir(hazy_dir))
        
        # 确保文件数量匹配
        assert len(self.clear_files) == len(self.hazy_files)
    
    def __len__(self):
        return len(self.clear_files)
    
    def __getitem__(self, idx):
        clear_path = os.path.join(self.clear_dir, self.clear_files[idx])
        hazy_path = os.path.join(self.hazy_dir, self.hazy_files[idx])
        
        clear_img = Image.open(clear_path).convert('RGB')
        hazy_img = Image.open(hazy_path).convert('RGB')
        
        if self.transform:
            clear_img = self.transform(clear_img)
            hazy_img = self.transform(hazy_img)
        
        return hazy_img, clear_img  # 输入, 目标

# 使用示例
transform = transforms.Compose([
    transforms.Resize((256, 256)),
    transforms.ToTensor(),
])

train_dataset = DehazingDataset(
    "dataset_split/train/clear",
    "dataset_split/train/hazy",
    transform=transform
)

test_dataset = DehazingDataset(
    "dataset_split/test/clear", 
    "dataset_split/test/hazy",
    transform=transform
)
```

### 3. 数据加载器
```python
from torch.utils.data import DataLoader

train_loader = DataLoader(
    train_dataset, 
    batch_size=16, 
    shuffle=True, 
    num_workers=4
)

test_loader = DataLoader(
    test_dataset, 
    batch_size=16, 
    shuffle=False, 
    num_workers=4
)
```

## 注意事项
1. 图像文件名保持一致性，确保配对正确
2. 建议在训练前对图像进行预处理（如归一化、尺寸调整）
3. 可以根据需要调整训练集/测试集比例
4. 如需验证集，可以进一步划分训练集

## 重新划分数据集
如果需要重新划分数据集，可以运行：
```bash
python split_dataset.py --train_ratio 0.8 --random_seed 42 --output_dir dataset_split
```

参数说明：
- `--train_ratio`: 训练集比例 (默认: 0.8)
- `--random_seed`: 随机种子 (默认: 42)
- `--output_dir`: 输出目录 (默认: dataset_split)
- `--input_dir`: 输入数据集目录 (默认: Foggy-Hazy License Plates Images)
