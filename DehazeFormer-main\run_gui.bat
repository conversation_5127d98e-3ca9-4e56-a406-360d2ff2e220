@echo off
echo Starting DehazeFormer GUI...
echo.

REM Check if we're in the correct directory
if not exist "gui_dehaze.py" (
    echo Error: gui_dehaze.py not found!
    echo Please make sure you're running this from the DehazeFormer-main directory.
    pause
    exit /b 1
)

REM Check if model file exists
if not exist "configs\indoor\dehazeformer-d.pth" (
    echo Warning: Model file not found at configs\indoor\dehazeformer-d.pth
    echo Please make sure you have downloaded the pretrained model.
    echo.
)

REM Run the GUI
python gui_dehaze.py

pause
