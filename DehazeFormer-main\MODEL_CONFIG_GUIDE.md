# DehazeFormer GUI 模型配置指南

## 概述

`gui_dehaze.py` 文件已经修改，在代码的最前面添加了模型配置区域，用户可以轻松更改不同的模型设置。

## 配置区域位置

配置区域位于文件的第 23-45 行，在所有 import 语句之后，类定义之前：

```python
# ==================== 模型配置区域 ====================
# 用户可以在这里修改模型设置

# 可用的模型列表 (模型名称: [显示名称, 模型函数, 描述])
# 注意：只包含实际可用的模型文件
AVAILABLE_MODELS = {
    'dehazeformer-b': ['DehazeFormer-B', 'dehazeformer_b', '基础模型，推荐使用，速度较快'],
    'dehazeformer-l': ['DehazeFormer-L', 'dehazeformer_l', '大模型，效果最好但速度较慢']
}

# 默认选择的模型
DEFAULT_MODEL = 'dehazeformer-b'

# 模型文件路径配置
MODEL_BASE_PATH = "D:\\PAchongtext\\dan107\\DehazeFormer-main\\configs\\indoor"

# 模型文件路径映射 (如果模型文件名与模型名不同，可以在这里配置)
MODEL_FILE_MAPPING = {
    'dehazeformer-b': 'dehazeformer-b.pth',
    'dehazeformer-l': 'dehazeformer-l.pth'
}

# ==================== 配置区域结束 ====================
```

## 如何修改配置

### 1. 添加新模型

如果您有其他模型文件，可以在 `AVAILABLE_MODELS` 字典中添加：

```python
AVAILABLE_MODELS = {
    'dehazeformer-b': ['DehazeFormer-B', 'dehazeformer_b', '基础模型，推荐使用，速度较快'],
    'dehazeformer-l': ['DehazeFormer-L', 'dehazeformer_l', '大模型，效果最好但速度较慢'],
    'dehazeformer-s': ['DehazeFormer-S', 'dehazeformer_s', '小模型，速度更快'],  # 新增
}
```

同时在 `MODEL_FILE_MAPPING` 中添加对应的文件映射：

```python
MODEL_FILE_MAPPING = {
    'dehazeformer-b': 'dehazeformer-b.pth',
    'dehazeformer-l': 'dehazeformer-l.pth',
    'dehazeformer-s': 'dehazeformer-s.pth',  # 新增
}
```

### 2. 更改默认模型

修改 `DEFAULT_MODEL` 变量：

```python
DEFAULT_MODEL = 'dehazeformer-l'  # 改为使用大模型作为默认
```

### 3. 更改模型文件路径

如果您的模型文件在不同位置，修改 `MODEL_BASE_PATH`：

```python
MODEL_BASE_PATH = "C:\\your\\custom\\path\\to\\models"
```

### 4. 自定义模型文件名

如果您的模型文件名与标准名称不同，在 `MODEL_FILE_MAPPING` 中修改：

```python
MODEL_FILE_MAPPING = {
    'dehazeformer-b': 'my_custom_b_model.pth',
    'dehazeformer-l': 'my_custom_l_model.pth'
}
```

## 新增功能

### GUI 中的模型选择

修改后的 GUI 现在包含：

1. **模型选择下拉框**：用户可以在运行时选择不同的模型
2. **重新加载模型按钮**：可以重新加载当前选择的模型
3. **模型状态显示**：显示当前加载的模型和运行设备（GPU/CPU）

### 动态模型切换

- 用户可以在 GUI 中选择不同的模型，无需重启程序
- 选择新模型后，之前的模型会被清除，需要点击"重新加载模型"或"开始去雾"时自动加载新模型
- 状态栏会显示当前选择和加载的模型信息

## 注意事项

1. **模型文件存在性**：确保 `MODEL_FILE_MAPPING` 中指定的模型文件确实存在于 `MODEL_BASE_PATH` 路径下
2. **模型函数名**：`AVAILABLE_MODELS` 中的模型函数名必须与 `models` 模块中的函数名一致
3. **路径格式**：Windows 路径中使用双反斜杠 `\\` 或原始字符串 `r"path"`

## 当前可用模型

根据您的说明，目前只有以下两个模型可用：

- **dehazeformer-b**：基础模型，推荐使用，速度较快
- **dehazeformer-l**：大模型，效果最好但速度较慢

如果您获得了其他模型文件，只需按照上述步骤添加到配置中即可。
