import os
import argparse
import torch
import torch.nn as nn
import numpy as np
import cv2
from collections import OrderedDict

from utils import read_img, write_img, hwc_to_chw, chw_to_hwc
from models import *


def single(save_dir):
    """Load model state dict and remove 'module.' prefix if present"""
    state_dict = torch.load(save_dir, map_location='cpu')['state_dict']
    new_state_dict = OrderedDict()

    for k, v in state_dict.items():
        if k.startswith('module.'):
            name = k[7:]  # remove 'module.' prefix
        else:
            name = k
        new_state_dict[name] = v

    return new_state_dict


def dehaze_single_image(model, image_path, output_path=None):
    """
    Dehaze a single image using the trained model
    
    Args:
        model: Trained DehazeFormer model
        image_path: Path to the input hazy image
        output_path: Path to save the dehazed image (optional)
    
    Returns:
        dehazed_image: Numpy array of the dehazed image
    """
    # Read and preprocess the image
    img = read_img(image_path)  # Read image and normalize to [0, 1]
    img = img * 2 - 1  # Scale to [-1, 1]
    
    # Convert to tensor format (CHW)
    img_tensor = torch.from_numpy(hwc_to_chw(img)).unsqueeze(0)
    
    # Move to GPU if available
    if torch.cuda.is_available():
        img_tensor = img_tensor.cuda()
    
    # Inference
    model.eval()
    with torch.no_grad():
        output = model(img_tensor).clamp_(-1, 1)
        
        # Convert back to [0, 1] range
        output = output * 0.5 + 0.5
        
        # Convert to numpy array
        dehazed_img = chw_to_hwc(output.detach().cpu().squeeze(0).numpy())
    
    # Save the result if output path is provided
    if output_path:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        write_img(output_path, dehazed_img)
        print(f"Dehazed image saved to: {output_path}")
    
    return dehazed_img


def main():
    parser = argparse.ArgumentParser(description='DehazeFormer Image Dehazing Inference')
    parser.add_argument('--input', '-i', required=True, type=str, 
                       help='Path to input hazy image or directory containing hazy images')
    parser.add_argument('--output', '-o', type=str, default='./dehazed_results/',
                       help='Path to save dehazed images (default: ./dehazed_results/)')
    parser.add_argument('--model', default='dehazeformer-b', type=str,
                       help='Model name (dehazeformer-t/s/b/d/w/m/l)')
    parser.add_argument('--model_path', type=str, default='./saved_models/indoor/dehazeformer-b.pth',
                       help='Path to the trained model weights')
    parser.add_argument('--gpu', action='store_true', help='Use GPU for inference')
    
    args = parser.parse_args()
    
    # Check if model file exists
    if not os.path.exists(args.model_path):
        print(f"Error: Model file not found at {args.model_path}")
        print("Please download the pretrained models from:")
        print("GoogleDrive: https://drive.google.com/drive/folders/1Yy_GH6_bydYPU6_JJzFQwig4LTh86VI4")
        print("BaiduPan: https://pan.baidu.com/s/1R7cFISQHaWmFzq_7LyQMXQ?pwd=hab1")
        return
    
    # Initialize model
    print(f"Loading model: {args.model}")
    network = eval(args.model.replace('-', '_'))()
    
    # Load pretrained weights
    network.load_state_dict(single(args.model_path))
    
    # Move to GPU if available and requested
    if args.gpu and torch.cuda.is_available():
        network.cuda()
        print("Using GPU for inference")
    elif args.gpu and not torch.cuda.is_available():
        print("GPU requested but not available, using CPU")
    else:
        print("Using CPU for inference")
    
    # Process input
    if os.path.isfile(args.input):
        # Single image processing
        print(f"Processing single image: {args.input}")
        
        # Generate output filename
        input_filename = os.path.basename(args.input)
        name, ext = os.path.splitext(input_filename)
        output_filename = f"{name}_dehazed{ext}"
        output_path = os.path.join(args.output, output_filename)
        
        # Dehaze the image
        dehazed_img = dehaze_single_image(network, args.input, output_path)
        print("Dehazing completed!")
        
    elif os.path.isdir(args.input):
        # Batch processing
        print(f"Processing images in directory: {args.input}")
        
        # Get all image files
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
        image_files = []
        for ext in image_extensions:
            image_files.extend([f for f in os.listdir(args.input) if f.lower().endswith(ext)])
        
        if not image_files:
            print("No image files found in the input directory")
            return
        
        print(f"Found {len(image_files)} images to process")
        
        # Process each image
        for i, img_file in enumerate(image_files, 1):
            input_path = os.path.join(args.input, img_file)
            
            # Generate output filename
            name, ext = os.path.splitext(img_file)
            output_filename = f"{name}_dehazed{ext}"
            output_path = os.path.join(args.output, output_filename)
            
            print(f"Processing {i}/{len(image_files)}: {img_file}")
            
            try:
                dehazed_img = dehaze_single_image(network, input_path, output_path)
            except Exception as e:
                print(f"Error processing {img_file}: {str(e)}")
                continue
        
        print("Batch processing completed!")
    
    else:
        print(f"Error: Input path {args.input} is neither a file nor a directory")


if __name__ == '__main__':
    main()
