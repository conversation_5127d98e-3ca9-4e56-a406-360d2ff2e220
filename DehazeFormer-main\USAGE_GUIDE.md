# DehazeFormer 使用指南

这是一个基于Vision Transformer的图像去雾模型，可以有效去除图像中的雾霾效果。

## 快速开始

### 1. 环境安装

首先确保您的系统已安装Python 3.7+和PyTorch。

```bash
# 创建虚拟环境（推荐）
conda create -n dehazeformer python=3.7
conda activate dehazeformer

# 安装PyTorch（根据您的CUDA版本选择）
# CPU版本
pip install torch torchvision

# GPU版本（CUDA 11.3）
conda install pytorch=1.10.2 torchvision torchaudio cudatoolkit=11.3 -c pytorch

# 安装其他依赖
pip install -r requirements.txt
```

### 2. 下载预训练模型

您需要下载预训练的模型权重：

**方法1：Google Drive**
1. 访问：https://drive.google.com/drive/folders/1Yy_GH6_bydYPU6_JJzFQwig4LTh86VI4
2. 下载 `indoor` 文件夹中的 `dehazeformer-b.pth`
3. 将文件放置在 `./saved_models/indoor/` 目录下

**方法2：百度网盘**
1. 访问：https://pan.baidu.com/s/1R7cFISQHaWmFzq_7LyQMXQ?pwd=hab1
2. 提取码：hab1
3. 下载模型文件并放置在正确位置

### 3. 使用方法

#### 方法1：简单使用（推荐）

```bash
# 处理单张图片
python simple_dehaze.py input_hazy_image.jpg

# 指定输出文件名
python simple_dehaze.py input_hazy_image.jpg output_clear_image.jpg
```

#### 方法2：高级使用

```bash
# 处理单张图片
python inference.py --input hazy_image.jpg --output ./results/

# 批量处理文件夹中的所有图片
python inference.py --input ./hazy_images/ --output ./results/

# 使用GPU加速
python inference.py --input hazy_image.jpg --output ./results/ --gpu

# 使用不同的模型
python inference.py --input hazy_image.jpg --model dehazeformer-s --model_path ./saved_models/indoor/dehazeformer-s.pth
```

### 4. 支持的模型

- `dehazeformer-t`: 最小模型，速度最快
- `dehazeformer-s`: 小模型，平衡速度和效果
- `dehazeformer-b`: 基础模型，推荐使用
- `dehazeformer-d`: 深度模型
- `dehazeformer-w`: 宽模型
- `dehazeformer-m`: 中等模型
- `dehazeformer-l`: 大模型，效果最好但速度较慢

### 5. 支持的图片格式

- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- TIFF (.tiff, .tif)

## 示例

### 处理单张图片
```bash
python simple_dehaze.py foggy_street.jpg clear_street.jpg
```

### 批量处理
```bash
python inference.py --input ./foggy_images/ --output ./clear_images/ --gpu
```

## 注意事项

1. **GPU内存**: 如果遇到GPU内存不足，可以使用CPU模式或者调整图片大小
2. **图片质量**: 模型在合成数据集上训练，对真实雾霾图片的效果可能有所差异
3. **处理时间**: GPU模式下处理速度更快，CPU模式下可能需要较长时间

## 故障排除

### 常见问题

1. **模型文件未找到**
   ```
   Error: Model file not found at ./saved_models/indoor/dehazeformer-b.pth
   ```
   解决方案：请按照上述步骤下载预训练模型

2. **CUDA内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   解决方案：使用CPU模式或减小输入图片尺寸

3. **依赖包缺失**
   ```
   ModuleNotFoundError: No module named 'xxx'
   ```
   解决方案：运行 `pip install -r requirements.txt`

### 性能优化

- 使用GPU可以显著提升处理速度
- 对于大批量处理，建议使用 `inference.py` 脚本
- 如果内存有限，可以考虑使用较小的模型（如 dehazeformer-s）

## 技术细节

该模型基于Vision Transformer架构，专门针对图像去雾任务进行了优化：
- 修改了归一化层和激活函数
- 改进了空间信息聚合方案
- 在SOTS数据集上达到了SOTA性能

更多技术细节请参考原论文：
- IEEE TIP: https://doi.org/10.1109/TIP.2023.3256763
- arXiv: https://arxiv.org/abs/2204.03883
